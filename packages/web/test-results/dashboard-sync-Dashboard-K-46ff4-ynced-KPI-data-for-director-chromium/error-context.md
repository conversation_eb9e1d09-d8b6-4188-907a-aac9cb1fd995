# Test info

- Name: Dashboard KPI Sync Tests >> should display synced KPI data for director
- Location: /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-sync.test.js:31:3

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('[data-testid="page-title"]:has-text("Dashboard")')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('[data-testid="page-title"]:has-text("Dashboard")')

    at /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-sync.test.js:41:34
```

# Test source

```ts
   1 | // PLW: Test dashboard sync functionality với KPI từ tasks và reports
   2 | import { test, expect } from '@playwright/test';
   3 |
   4 | test.describe('Dashboard KPI Sync Tests', () => {
   5 |   test.beforeEach(async ({ page }) => {
   6 |     // Navigate và auto-login
   7 |     await page.goto('http://localhost:8088');
   8 |     await page.waitForLoadState('networkidle');
   9 |     
   10 |     // Setup test auth
   11 |     await page.evaluate(() => {
   12 |       const testUser = {
   13 |         id: 'test-user-001',
   14 |         name: 'Khổng Đức Mạnh',
   15 |         email: '<EMAIL>',
   16 |         role: 'director',
   17 |         team: 'Phòng Kinh Doanh',
   18 |         location: 'Hà Nội',
   19 |         password_changed: true,
   20 |       };
   21 |       localStorage.setItem('currentUser', JSON.stringify(testUser));
   22 |       localStorage.setItem('authToken', 'test-auth-token');
   23 |       localStorage.setItem('isAuthenticated', 'true');
   24 |     });
   25 |     
   26 |     await page.reload();
   27 |     await page.waitForLoadState('networkidle');
   28 |     await page.waitForTimeout(3000);
   29 |   });
   30 |
   31 |   test('should display synced KPI data for director', async ({ page }) => {
   32 |     console.log('🧪 Testing director dashboard KPI sync...');
   33 |     
   34 |     // Navigate to dashboard
   35 |     await page.goto('http://localhost:8088/');
   36 |     await page.waitForLoadState('networkidle');
   37 |     await page.waitForTimeout(2000);
   38 |     
   39 |     // Check if dashboard loaded
   40 |     const dashboardTitle = page.locator('[data-testid="page-title"]:has-text("Dashboard")');
>  41 |     await expect(dashboardTitle).toBeVisible();
      |                                  ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
   42 |     console.log('✅ Dashboard loaded');
   43 |     
   44 |     // Check summary stats section
   45 |     const summarySection = page.locator('.bg-gradient-to-r.from-blue-50');
   46 |     await expect(summarySection).toBeVisible();
   47 |     console.log('✅ Summary section visible');
   48 |     
   49 |     // Check KPI cards
   50 |     const kpiGrid = page.locator('[data-testid="kpi-cards-grid"]');
   51 |     await expect(kpiGrid).toBeVisible();
   52 |
   53 |     const kpiCards = kpiGrid.locator('.bg-white\\/95, .border-yellow-200, .border-blue-200');
   54 |     const cardCount = await kpiCards.count();
   55 |     console.log(`📊 Found ${cardCount} KPI cards`);
   56 |     expect(cardCount).toBeGreaterThanOrEqual(4); // KTS, KH/CĐT, SBG, Doanh số
   57 |
   58 |     // Check specific KPI titles for director
   59 |     await expect(page.locator('text=Tổng KTS')).toBeVisible();
   60 |     await expect(page.locator('text=Tổng KH/CĐT')).toBeVisible();
   61 |     await expect(page.locator('text=Tổng SBG')).toBeVisible();
   62 |     await expect(page.locator('text=Tổng Doanh Số')).toBeVisible();
   63 |     console.log('✅ Director KPI titles correct');
   64 |     
   65 |     // Check charts visibility (director should see all charts)
   66 |     const charts = page.locator('.lg\\:col-span-2, .lg\\:col-span-1');
   67 |     const chartCount = await charts.count();
   68 |     console.log(`📈 Found ${chartCount} chart sections`);
   69 |     expect(chartCount).toBeGreaterThan(0);
   70 |     
   71 |     console.log('✅ Director dashboard test passed');
   72 |   });
   73 |
   74 |   test('should test team leader permissions', async ({ page }) => {
   75 |     console.log('🧪 Testing team leader dashboard permissions...');
   76 |     
   77 |     // Change user to team leader
   78 |     await page.evaluate(() => {
   79 |       const teamLeaderUser = {
   80 |         id: 'team-leader-001',
   81 |         name: 'Lương Việt Anh',
   82 |         email: '<EMAIL>',
   83 |         role: 'team_leader',
   84 |         team: 'Nhóm 1',
   85 |         location: 'Hà Nội',
   86 |         password_changed: true,
   87 |       };
   88 |       localStorage.setItem('currentUser', JSON.stringify(teamLeaderUser));
   89 |     });
   90 |     
   91 |     await page.reload();
   92 |     await page.waitForLoadState('networkidle');
   93 |     await page.waitForTimeout(2000);
   94 |     
   95 |     // Check KPI titles for team leader
   96 |     await expect(page.locator('text=KTS Nhóm')).toBeVisible();
   97 |     await expect(page.locator('text=KH/CĐT Nhóm')).toBeVisible();
   98 |     await expect(page.locator('text=SBG Nhóm')).toBeVisible();
   99 |     console.log('✅ Team leader KPI titles correct');
  100 |     
  101 |     // Team leader should see some charts but not all
  102 |     const charts = page.locator('.lg\\:col-span-2, .lg\\:col-span-1');
  103 |     const chartCount = await charts.count();
  104 |     console.log(`📈 Team leader sees ${chartCount} chart sections`);
  105 |     
  106 |     console.log('✅ Team leader dashboard test passed');
  107 |   });
  108 |
  109 |   test('should test employee permissions', async ({ page }) => {
  110 |     console.log('🧪 Testing employee dashboard permissions...');
  111 |     
  112 |     // Change user to employee
  113 |     await page.evaluate(() => {
  114 |       const employeeUser = {
  115 |         id: 'employee-001',
  116 |         name: 'Nguyễn Văn A',
  117 |         email: '<EMAIL>',
  118 |         role: 'employee',
  119 |         team: 'Nhóm 1',
  120 |         location: 'Hà Nội',
  121 |         password_changed: true,
  122 |       };
  123 |       localStorage.setItem('currentUser', JSON.stringify(employeeUser));
  124 |     });
  125 |     
  126 |     await page.reload();
  127 |     await page.waitForLoadState('networkidle');
  128 |     await page.waitForTimeout(2000);
  129 |     
  130 |     // Check KPI titles for employee (should show personal data)
  131 |     await expect(page.locator('text=KTS Cá nhân')).toBeVisible();
  132 |     await expect(page.locator('text=KH/CĐT Cá nhân')).toBeVisible();
  133 |     await expect(page.locator('text=SBG Cá nhân')).toBeVisible();
  134 |     console.log('✅ Employee KPI titles correct');
  135 |     
  136 |     // Employee should see limited charts
  137 |     const advancedCharts = page.locator('.lg\\:col-span-2');
  138 |     const advancedChartCount = await advancedCharts.count();
  139 |     console.log(`📈 Employee sees ${advancedChartCount} advanced charts`);
  140 |     
  141 |     console.log('✅ Employee dashboard test passed');
```