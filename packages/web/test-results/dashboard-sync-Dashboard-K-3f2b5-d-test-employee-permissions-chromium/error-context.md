# Test info

- Name: Dashboard KPI Sync Tests >> should test employee permissions
- Location: /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-sync.test.js:109:3

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('text=KTS Cá nhân')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('text=KTS Cá nhân')

    at /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-sync.test.js:131:52
```

# Test source

```ts
   31 |   test('should display synced KPI data for director', async ({ page }) => {
   32 |     console.log('🧪 Testing director dashboard KPI sync...');
   33 |     
   34 |     // Navigate to dashboard
   35 |     await page.goto('http://localhost:8088/');
   36 |     await page.waitForLoadState('networkidle');
   37 |     await page.waitForTimeout(2000);
   38 |     
   39 |     // Check if dashboard loaded
   40 |     const dashboardTitle = page.locator('[data-testid="page-title"]:has-text("Dashboard")');
   41 |     await expect(dashboardTitle).toBeVisible();
   42 |     console.log('✅ Dashboard loaded');
   43 |     
   44 |     // Check summary stats section
   45 |     const summarySection = page.locator('.bg-gradient-to-r.from-blue-50');
   46 |     await expect(summarySection).toBeVisible();
   47 |     console.log('✅ Summary section visible');
   48 |     
   49 |     // Check KPI cards
   50 |     const kpiGrid = page.locator('[data-testid="kpi-cards-grid"]');
   51 |     await expect(kpiGrid).toBeVisible();
   52 |
   53 |     const kpiCards = kpiGrid.locator('.bg-white\\/95, .border-yellow-200, .border-blue-200');
   54 |     const cardCount = await kpiCards.count();
   55 |     console.log(`📊 Found ${cardCount} KPI cards`);
   56 |     expect(cardCount).toBeGreaterThanOrEqual(4); // KTS, KH/CĐT, SBG, Doanh số
   57 |
   58 |     // Check specific KPI titles for director
   59 |     await expect(page.locator('text=Tổng KTS')).toBeVisible();
   60 |     await expect(page.locator('text=Tổng KH/CĐT')).toBeVisible();
   61 |     await expect(page.locator('text=Tổng SBG')).toBeVisible();
   62 |     await expect(page.locator('text=Tổng Doanh Số')).toBeVisible();
   63 |     console.log('✅ Director KPI titles correct');
   64 |     
   65 |     // Check charts visibility (director should see all charts)
   66 |     const charts = page.locator('.lg\\:col-span-2, .lg\\:col-span-1');
   67 |     const chartCount = await charts.count();
   68 |     console.log(`📈 Found ${chartCount} chart sections`);
   69 |     expect(chartCount).toBeGreaterThan(0);
   70 |     
   71 |     console.log('✅ Director dashboard test passed');
   72 |   });
   73 |
   74 |   test('should test team leader permissions', async ({ page }) => {
   75 |     console.log('🧪 Testing team leader dashboard permissions...');
   76 |     
   77 |     // Change user to team leader
   78 |     await page.evaluate(() => {
   79 |       const teamLeaderUser = {
   80 |         id: 'team-leader-001',
   81 |         name: 'Lương Việt Anh',
   82 |         email: '<EMAIL>',
   83 |         role: 'team_leader',
   84 |         team: 'Nhóm 1',
   85 |         location: 'Hà Nội',
   86 |         password_changed: true,
   87 |       };
   88 |       localStorage.setItem('currentUser', JSON.stringify(teamLeaderUser));
   89 |     });
   90 |     
   91 |     await page.reload();
   92 |     await page.waitForLoadState('networkidle');
   93 |     await page.waitForTimeout(2000);
   94 |     
   95 |     // Check KPI titles for team leader
   96 |     await expect(page.locator('text=KTS Nhóm')).toBeVisible();
   97 |     await expect(page.locator('text=KH/CĐT Nhóm')).toBeVisible();
   98 |     await expect(page.locator('text=SBG Nhóm')).toBeVisible();
   99 |     console.log('✅ Team leader KPI titles correct');
  100 |     
  101 |     // Team leader should see some charts but not all
  102 |     const charts = page.locator('.lg\\:col-span-2, .lg\\:col-span-1');
  103 |     const chartCount = await charts.count();
  104 |     console.log(`📈 Team leader sees ${chartCount} chart sections`);
  105 |     
  106 |     console.log('✅ Team leader dashboard test passed');
  107 |   });
  108 |
  109 |   test('should test employee permissions', async ({ page }) => {
  110 |     console.log('🧪 Testing employee dashboard permissions...');
  111 |     
  112 |     // Change user to employee
  113 |     await page.evaluate(() => {
  114 |       const employeeUser = {
  115 |         id: 'employee-001',
  116 |         name: 'Nguyễn Văn A',
  117 |         email: '<EMAIL>',
  118 |         role: 'employee',
  119 |         team: 'Nhóm 1',
  120 |         location: 'Hà Nội',
  121 |         password_changed: true,
  122 |       };
  123 |       localStorage.setItem('currentUser', JSON.stringify(employeeUser));
  124 |     });
  125 |     
  126 |     await page.reload();
  127 |     await page.waitForLoadState('networkidle');
  128 |     await page.waitForTimeout(2000);
  129 |     
  130 |     // Check KPI titles for employee (should show personal data)
> 131 |     await expect(page.locator('text=KTS Cá nhân')).toBeVisible();
      |                                                    ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  132 |     await expect(page.locator('text=KH/CĐT Cá nhân')).toBeVisible();
  133 |     await expect(page.locator('text=SBG Cá nhân')).toBeVisible();
  134 |     console.log('✅ Employee KPI titles correct');
  135 |     
  136 |     // Employee should see limited charts
  137 |     const advancedCharts = page.locator('.lg\\:col-span-2');
  138 |     const advancedChartCount = await advancedCharts.count();
  139 |     console.log(`📈 Employee sees ${advancedChartCount} advanced charts`);
  140 |     
  141 |     console.log('✅ Employee dashboard test passed');
  142 |   });
  143 |
  144 |   test('should verify KPI data synchronization', async ({ page }) => {
  145 |     console.log('🧪 Testing KPI data synchronization...');
  146 |     
  147 |     // Go to tasks page first to create/complete some tasks
  148 |     await page.goto('http://localhost:8088/tasks');
  149 |     await page.waitForLoadState('networkidle');
  150 |     await page.waitForTimeout(2000);
  151 |     
  152 |     // Check if tasks exist
  153 |     const tasksTable = page.locator('[data-testid="tasks-table"]');
  154 |     if (await tasksTable.isVisible()) {
  155 |       const taskRows = page.locator('table tbody tr');
  156 |       const taskCount = await taskRows.count();
  157 |       console.log(`📋 Found ${taskCount} tasks`);
  158 |       
  159 |       // Go back to dashboard
  160 |       await page.goto('http://localhost:8088/');
  161 |       await page.waitForLoadState('networkidle');
  162 |       await page.waitForTimeout(2000);
  163 |       
  164 |       // Check if summary stats reflect task data
  165 |       const totalTasksElement = page.locator('text=Tổng công việc').locator('..').locator('.text-2xl');
  166 |       if (await totalTasksElement.isVisible()) {
  167 |         const totalTasksText = await totalTasksElement.textContent();
  168 |         console.log(`📊 Dashboard shows ${totalTasksText} total tasks`);
  169 |       }
  170 |       
  171 |       // Check if KPI values are not zero (indicating sync is working)
  172 |       const kpiValues = page.locator('.text-3xl.font-bold.text-\\[\\#2d3436\\]');
  173 |       const valueCount = await kpiValues.count();
  174 |       
  175 |       for (let i = 0; i < Math.min(valueCount, 4); i++) {
  176 |         const value = await kpiValues.nth(i).textContent();
  177 |         console.log(`📈 KPI ${i + 1} value: ${value}`);
  178 |       }
  179 |       
  180 |       console.log('✅ KPI synchronization verified');
  181 |     } else {
  182 |       console.log('⚠️ No tasks table found, skipping sync verification');
  183 |     }
  184 |   });
  185 |
  186 |   test('should test reports data integration', async ({ page }) => {
  187 |     console.log('🧪 Testing reports data integration...');
  188 |     
  189 |     // Check if reports menu is accessible (for director)
  190 |     const reportsLink = page.locator('text=Báo cáo');
  191 |     if (await reportsLink.isVisible()) {
  192 |       await reportsLink.click();
  193 |       await page.waitForLoadState('networkidle');
  194 |       await page.waitForTimeout(2000);
  195 |       
  196 |       // Check if reports page loaded
  197 |       const reportsContent = page.locator('text=Báo cáo hiệu suất');
  198 |       if (await reportsContent.isVisible()) {
  199 |         console.log('✅ Reports page accessible');
  200 |         
  201 |         // Go back to dashboard
  202 |         await page.goto('http://localhost:8088/');
  203 |         await page.waitForLoadState('networkidle');
  204 |         await page.waitForTimeout(2000);
  205 |         
  206 |         // Check if sales data is reflected in dashboard
  207 |         const salesKpi = page.locator('text=Tổng Doanh Số').locator('..').locator('.text-3xl');
  208 |         if (await salesKpi.isVisible()) {
  209 |           const salesValue = await salesKpi.textContent();
  210 |           console.log(`💰 Dashboard sales KPI: ${salesValue}`);
  211 |           expect(salesValue).not.toBe('0');
  212 |         }
  213 |         
  214 |         console.log('✅ Reports integration verified');
  215 |       }
  216 |     } else {
  217 |       console.log('⚠️ Reports not accessible for current user');
  218 |     }
  219 |   });
  220 |
  221 |   test('should test responsive dashboard layout', async ({ page }) => {
  222 |     console.log('🧪 Testing responsive dashboard layout...');
  223 |     
  224 |     // Test mobile viewport
  225 |     await page.setViewportSize({ width: 375, height: 667 });
  226 |     await page.waitForTimeout(1000);
  227 |     
  228 |     // Check if KPI cards stack properly on mobile
  229 |     const kpiGrid = page.locator('[data-testid="kpi-cards-grid"]');
  230 |     await expect(kpiGrid).toBeVisible();
  231 |     console.log('✅ Mobile layout responsive');
```