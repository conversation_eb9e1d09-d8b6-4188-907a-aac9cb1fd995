# Test info

- Name: Task Delete Functionality >> should delete task from table row delete button
- Location: /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/task-delete.test.js:25:3

# Error details

```
Error: expect(received).toBe(expected) // Object.is equality

Expected: 0
Received: 1
    at /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/task-delete.test.js:65:28
```

# Page snapshot

```yaml
- region "Notifications (F8)":
  - list:
    - status:
      - img
      - text: Đăng nhập thành công Chào mừng bạn quay trở lại!
      - button:
        - img
- region "Notifications alt+T"
- img "Logo"
- text: Phòng Kinh Doanh
- button "Mở rộng sidebar":
  - img
- navigation:
  - link "Dashboard Dashboard":
    - /url: /
    - img
    - text: Dashboard Dashboard
  - link "Công việc Công việc":
    - /url: /tasks
    - img
    - text: <PERSON><PERSON>ng vi<PERSON>c Công việc
  - link "<PERSON>ế hoạch Kế hoạch":
    - /url: /calendar
    - img
    - text: <PERSON><PERSON> hoạch Kế hoạch
  - link "Báo cáo Báo cáo":
    - /url: /reports
    - img
    - text: Báo cáo Báo cáo
  - link "Nhân viên Nhân viên":
    - /url: /employees
    - img
    - text: Nhân viên Nhân viên
- button "KĐ Khổng Đức Mạnh <EMAIL> Khổng Đức Mạnh":
  - text: KĐ
  - paragraph: Khổng Đức Mạnh
  - paragraph: <EMAIL>
  - text: Khổng Đức Mạnh
- main:
  - heading "Quản lý công việc" [level=1]
  - button:
    - img
  - button "Xuất dữ liệu":
    - img
  - button "Tạo công việc":
    - img
    - text: Tạo công việc
  - button "Của tôi":
    - img
    - text: Của tôi
  - button "Của nhóm":
    - img
    - text: Của nhóm
  - button "Thành viên":
    - img
    - text: Thành viên
  - button "Chung":
    - img
    - text: Chung
  - button:
    - img
  - table:
    - rowgroup:
      - row "Tiêu đề Trạng thái & Ưu tiên Người làm Tới hạn Tương tác":
        - cell "Tiêu đề"
        - cell "Trạng thái & Ưu tiên"
        - cell "Người làm"
        - cell "Tới hạn"
        - cell "Tương tác"
    - rowgroup:
      - row "Công việc chung của cả phòng Không xác định 9/6/2025":
        - cell "Công việc chung của cả phòng":
          - img
          - text: Công việc chung của cả phòng
        - cell:
          - 'button "Chưa bắt đầu - Click để chuyển sang: Đang thực hiện"':
            - img
          - 'button "Cao - Click để chuyển sang: Thấp"':
            - img
        - cell "Không xác định"
        - cell "9/6/2025"
        - cell:
          - button "Chỉnh sửa":
            - img
          - button "Xóa":
            - img
  - button "Trước"
  - button "1"
  - button "Sau"
- region "Notifications (F8)":
  - list:
    - status:
      - img
      - text: Đăng nhập thành công Chào mừng bạn quay trở lại!
      - button:
        - img
- region "Notifications alt+T"
```

# Test source

```ts
   1 | // Test script để kiểm tra chức năng xóa công việc
   2 | // Sử dụng Playwright để test UI interactions
   3 |
   4 | import { test, expect } from '@playwright/test';
   5 |
   6 | test.describe('Task Delete Functionality', () => {
   7 |   test.beforeEach(async ({ page }) => {
   8 |     // Navigate to the task management page
   9 |     await page.goto('http://localhost:8088');
   10 |     
   11 |     // Wait for the page to load
   12 |     await page.waitForLoadState('networkidle');
   13 |     
   14 |     // Navigate to tasks menu if needed
   15 |     await page.click('[data-testid="tasks-menu"]', { timeout: 10000 }).catch(() => {
   16 |       // If data-testid doesn't exist, try alternative selectors
   17 |       return page.click('text=Công việc').catch(() => {
   18 |         return page.click('a[href*="tasks"]');
   19 |       });
   20 |     });
   21 |     
   22 |     await page.waitForTimeout(2000);
   23 |   });
   24 |
   25 |   test('should delete task from table row delete button', async ({ page }) => {
   26 |     console.log('🧪 Testing table row delete button...');
   27 |     
   28 |     // Wait for task list to load
   29 |     await page.waitForSelector('table tbody tr', { timeout: 10000 });
   30 |     
   31 |     // Get initial task count
   32 |     const initialTaskCount = await page.locator('table tbody tr').count();
   33 |     console.log(`📊 Initial task count: ${initialTaskCount}`);
   34 |     
   35 |     if (initialTaskCount === 0) {
   36 |       console.log('⚠️ No tasks found to delete');
   37 |       return;
   38 |     }
   39 |     
   40 |     // Find the first task's delete button
   41 |     const firstTaskRow = page.locator('table tbody tr').first();
   42 |     const deleteButton = firstTaskRow.locator('button[title="Xóa"]');
   43 |     
   44 |     // Get task title for confirmation
   45 |     const taskTitle = await firstTaskRow.locator('td').first().textContent();
   46 |     console.log(`🎯 Attempting to delete task: "${taskTitle}"`);
   47 |     
   48 |     // Click delete button
   49 |     await deleteButton.click();
   50 |     
   51 |     // Handle confirmation dialog
   52 |     page.on('dialog', async dialog => {
   53 |       console.log(`💬 Dialog message: ${dialog.message()}`);
   54 |       expect(dialog.message()).toContain('Bạn có chắc muốn xóa');
   55 |       await dialog.accept();
   56 |     });
   57 |     
   58 |     // Wait for task to be removed from UI
   59 |     await page.waitForTimeout(2000);
   60 |     
   61 |     // Verify task count decreased
   62 |     const finalTaskCount = await page.locator('table tbody tr').count();
   63 |     console.log(`📊 Final task count: ${finalTaskCount}`);
   64 |     
>  65 |     expect(finalTaskCount).toBe(initialTaskCount - 1);
      |                            ^ Error: expect(received).toBe(expected) // Object.is equality
   66 |     console.log('✅ Table row delete test passed');
   67 |   });
   68 |
   69 |   test('should delete task from detail panel delete button', async ({ page }) => {
   70 |     console.log('🧪 Testing detail panel delete button...');
   71 |     
   72 |     // Wait for task list to load
   73 |     await page.waitForSelector('table tbody tr', { timeout: 10000 });
   74 |     
   75 |     const initialTaskCount = await page.locator('table tbody tr').count();
   76 |     console.log(`📊 Initial task count: ${initialTaskCount}`);
   77 |     
   78 |     if (initialTaskCount === 0) {
   79 |       console.log('⚠️ No tasks found to delete');
   80 |       return;
   81 |     }
   82 |     
   83 |     // Click edit button to open detail panel
   84 |     const firstTaskRow = page.locator('table tbody tr').first();
   85 |     const editButton = firstTaskRow.locator('button[title="Chỉnh sửa"]');
   86 |     
   87 |     const taskTitle = await firstTaskRow.locator('td').first().textContent();
   88 |     console.log(`🎯 Opening detail panel for task: "${taskTitle}"`);
   89 |     
   90 |     await editButton.click();
   91 |     
   92 |     // Wait for detail panel to open
   93 |     await page.waitForSelector('.task-detail-panel', { timeout: 5000 });
   94 |     console.log('📋 Detail panel opened');
   95 |     
   96 |     // Find and click delete button in detail panel
   97 |     const detailDeleteButton = page.locator('.task-detail-panel button:has-text("Xóa")');
   98 |     await detailDeleteButton.click();
   99 |     
  100 |     // Handle confirmation dialog
  101 |     page.on('dialog', async dialog => {
  102 |       console.log(`💬 Dialog message: ${dialog.message()}`);
  103 |       expect(dialog.message()).toContain('Bạn có chắc muốn xóa');
  104 |       await dialog.accept();
  105 |     });
  106 |     
  107 |     // Wait for modal to close and task to be removed
  108 |     await page.waitForTimeout(3000);
  109 |     
  110 |     // Verify modal is closed
  111 |     await expect(page.locator('.task-detail-panel')).not.toBeVisible();
  112 |     console.log('📋 Detail panel closed');
  113 |     
  114 |     // Verify task count decreased
  115 |     const finalTaskCount = await page.locator('table tbody tr').count();
  116 |     console.log(`📊 Final task count: ${finalTaskCount}`);
  117 |     
  118 |     expect(finalTaskCount).toBe(initialTaskCount - 1);
  119 |     console.log('✅ Detail panel delete test passed');
  120 |   });
  121 |
  122 |   test('should show proper error when user lacks delete permissions', async ({ page }) => {
  123 |     console.log('🧪 Testing delete permissions...');
  124 |     
  125 |     // This test would require switching to a user without delete permissions
  126 |     // For now, we'll just verify the permission check exists
  127 |     
  128 |     await page.waitForSelector('table tbody tr', { timeout: 10000 });
  129 |     
  130 |     const taskCount = await page.locator('table tbody tr').count();
  131 |     if (taskCount === 0) {
  132 |       console.log('⚠️ No tasks found for permission test');
  133 |       return;
  134 |     }
  135 |     
  136 |     // Check if delete buttons are present (indicating user has permissions)
  137 |     const deleteButtons = await page.locator('button[title="Xóa"]').count();
  138 |     console.log(`🔐 Found ${deleteButtons} delete buttons (permission check)`);
  139 |     
  140 |     expect(deleteButtons).toBeGreaterThanOrEqual(0);
  141 |     console.log('✅ Permission check test passed');
  142 |   });
  143 | });
  144 |
```