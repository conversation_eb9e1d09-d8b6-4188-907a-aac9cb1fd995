# Test info

- Name: Dashboard KPI Sync Tests >> should test employee permissions
- Location: /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-sync.test.js:104:3

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('text=KTS Cá nhân, text=KH/CĐT Cá nhân, text=SBG Cá nhân')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('text=KTS Cá nhân, text=KH/CĐT Cá nhân, text=SBG Cá nhân')

    at /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-sync.test.js:126:91
```

# Test source

```ts
   26 |     await page.reload();
   27 |     await page.waitForLoadState('networkidle');
   28 |     await page.waitForTimeout(3000);
   29 |   });
   30 |
   31 |   test('should display synced KPI data for director', async ({ page }) => {
   32 |     console.log('🧪 Testing director dashboard KPI sync...');
   33 |     
   34 |     // Navigate to dashboard
   35 |     await page.goto('http://localhost:8088/');
   36 |     await page.waitForLoadState('networkidle');
   37 |     await page.waitForTimeout(2000);
   38 |     
   39 |     // Check if dashboard loaded
   40 |     const dashboardTitle = page.locator('h1:has-text("Dashboard")');
   41 |     await expect(dashboardTitle).toBeVisible();
   42 |     console.log('✅ Dashboard loaded');
   43 |     
   44 |     // Check summary stats section
   45 |     const summarySection = page.locator('.bg-gradient-to-r.from-blue-50');
   46 |     await expect(summarySection).toBeVisible();
   47 |     console.log('✅ Summary section visible');
   48 |     
   49 |     // Check KPI cards
   50 |     const kpiCards = page.locator('.bg-white\\/95.backdrop-blur-lg.rounded-\\[20px\\]');
   51 |     const cardCount = await kpiCards.count();
   52 |     console.log(`📊 Found ${cardCount} KPI cards`);
   53 |     expect(cardCount).toBeGreaterThanOrEqual(4); // KTS, KH/CĐT, SBG, Doanh số
   54 |     
   55 |     // Check specific KPI titles for director
   56 |     await expect(page.locator('text=Tổng KTS')).toBeVisible();
   57 |     await expect(page.locator('text=Tổng KH/CĐT')).toBeVisible();
   58 |     await expect(page.locator('text=Tổng SBG')).toBeVisible();
   59 |     await expect(page.locator('text=Tổng Doanh Số')).toBeVisible();
   60 |     console.log('✅ Director KPI titles correct');
   61 |     
   62 |     // Check charts visibility (director should see all charts)
   63 |     const charts = page.locator('.lg\\:col-span-2, .lg\\:col-span-1');
   64 |     const chartCount = await charts.count();
   65 |     console.log(`📈 Found ${chartCount} chart sections`);
   66 |     expect(chartCount).toBeGreaterThan(0);
   67 |     
   68 |     console.log('✅ Director dashboard test passed');
   69 |   });
   70 |
   71 |   test('should test team leader permissions', async ({ page }) => {
   72 |     console.log('🧪 Testing team leader dashboard permissions...');
   73 |     
   74 |     // Change user to team leader
   75 |     await page.evaluate(() => {
   76 |       const teamLeaderUser = {
   77 |         id: 'team-leader-001',
   78 |         name: 'Lương Việt Anh',
   79 |         email: '<EMAIL>',
   80 |         role: 'team_leader',
   81 |         team: 'Nhóm 1',
   82 |         location: 'Hà Nội',
   83 |         password_changed: true,
   84 |       };
   85 |       localStorage.setItem('currentUser', JSON.stringify(teamLeaderUser));
   86 |     });
   87 |     
   88 |     await page.reload();
   89 |     await page.waitForLoadState('networkidle');
   90 |     await page.waitForTimeout(2000);
   91 |     
   92 |     // Check KPI titles for team leader
   93 |     await expect(page.locator('text=KTS Nhóm, text=KH/CĐT Nhóm, text=SBG Nhóm')).toBeVisible();
   94 |     console.log('✅ Team leader KPI titles correct');
   95 |     
   96 |     // Team leader should see some charts but not all
   97 |     const charts = page.locator('.lg\\:col-span-2, .lg\\:col-span-1');
   98 |     const chartCount = await charts.count();
   99 |     console.log(`📈 Team leader sees ${chartCount} chart sections`);
  100 |     
  101 |     console.log('✅ Team leader dashboard test passed');
  102 |   });
  103 |
  104 |   test('should test employee permissions', async ({ page }) => {
  105 |     console.log('🧪 Testing employee dashboard permissions...');
  106 |     
  107 |     // Change user to employee
  108 |     await page.evaluate(() => {
  109 |       const employeeUser = {
  110 |         id: 'employee-001',
  111 |         name: 'Nguyễn Văn A',
  112 |         email: '<EMAIL>',
  113 |         role: 'employee',
  114 |         team: 'Nhóm 1',
  115 |         location: 'Hà Nội',
  116 |         password_changed: true,
  117 |       };
  118 |       localStorage.setItem('currentUser', JSON.stringify(employeeUser));
  119 |     });
  120 |     
  121 |     await page.reload();
  122 |     await page.waitForLoadState('networkidle');
  123 |     await page.waitForTimeout(2000);
  124 |     
  125 |     // Check KPI titles for employee (should show personal data)
> 126 |     await expect(page.locator('text=KTS Cá nhân, text=KH/CĐT Cá nhân, text=SBG Cá nhân')).toBeVisible();
      |                                                                                           ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  127 |     console.log('✅ Employee KPI titles correct');
  128 |     
  129 |     // Employee should see limited charts
  130 |     const advancedCharts = page.locator('.lg\\:col-span-2');
  131 |     const advancedChartCount = await advancedCharts.count();
  132 |     console.log(`📈 Employee sees ${advancedChartCount} advanced charts`);
  133 |     
  134 |     console.log('✅ Employee dashboard test passed');
  135 |   });
  136 |
  137 |   test('should verify KPI data synchronization', async ({ page }) => {
  138 |     console.log('🧪 Testing KPI data synchronization...');
  139 |     
  140 |     // Go to tasks page first to create/complete some tasks
  141 |     await page.goto('http://localhost:8088/tasks');
  142 |     await page.waitForLoadState('networkidle');
  143 |     await page.waitForTimeout(2000);
  144 |     
  145 |     // Check if tasks exist
  146 |     const tasksTable = page.locator('[data-testid="tasks-table"]');
  147 |     if (await tasksTable.isVisible()) {
  148 |       const taskRows = page.locator('table tbody tr');
  149 |       const taskCount = await taskRows.count();
  150 |       console.log(`📋 Found ${taskCount} tasks`);
  151 |       
  152 |       // Go back to dashboard
  153 |       await page.goto('http://localhost:8088/');
  154 |       await page.waitForLoadState('networkidle');
  155 |       await page.waitForTimeout(2000);
  156 |       
  157 |       // Check if summary stats reflect task data
  158 |       const totalTasksElement = page.locator('text=Tổng công việc').locator('..').locator('.text-2xl');
  159 |       if (await totalTasksElement.isVisible()) {
  160 |         const totalTasksText = await totalTasksElement.textContent();
  161 |         console.log(`📊 Dashboard shows ${totalTasksText} total tasks`);
  162 |       }
  163 |       
  164 |       // Check if KPI values are not zero (indicating sync is working)
  165 |       const kpiValues = page.locator('.text-3xl.font-bold.text-\\[\\#2d3436\\]');
  166 |       const valueCount = await kpiValues.count();
  167 |       
  168 |       for (let i = 0; i < Math.min(valueCount, 4); i++) {
  169 |         const value = await kpiValues.nth(i).textContent();
  170 |         console.log(`📈 KPI ${i + 1} value: ${value}`);
  171 |       }
  172 |       
  173 |       console.log('✅ KPI synchronization verified');
  174 |     } else {
  175 |       console.log('⚠️ No tasks table found, skipping sync verification');
  176 |     }
  177 |   });
  178 |
  179 |   test('should test reports data integration', async ({ page }) => {
  180 |     console.log('🧪 Testing reports data integration...');
  181 |     
  182 |     // Check if reports menu is accessible (for director)
  183 |     const reportsLink = page.locator('text=Báo cáo');
  184 |     if (await reportsLink.isVisible()) {
  185 |       await reportsLink.click();
  186 |       await page.waitForLoadState('networkidle');
  187 |       await page.waitForTimeout(2000);
  188 |       
  189 |       // Check if reports page loaded
  190 |       const reportsContent = page.locator('text=Báo cáo hiệu suất');
  191 |       if (await reportsContent.isVisible()) {
  192 |         console.log('✅ Reports page accessible');
  193 |         
  194 |         // Go back to dashboard
  195 |         await page.goto('http://localhost:8088/');
  196 |         await page.waitForLoadState('networkidle');
  197 |         await page.waitForTimeout(2000);
  198 |         
  199 |         // Check if sales data is reflected in dashboard
  200 |         const salesKpi = page.locator('text=Tổng Doanh Số').locator('..').locator('.text-3xl');
  201 |         if (await salesKpi.isVisible()) {
  202 |           const salesValue = await salesKpi.textContent();
  203 |           console.log(`💰 Dashboard sales KPI: ${salesValue}`);
  204 |           expect(salesValue).not.toBe('0');
  205 |         }
  206 |         
  207 |         console.log('✅ Reports integration verified');
  208 |       }
  209 |     } else {
  210 |       console.log('⚠️ Reports not accessible for current user');
  211 |     }
  212 |   });
  213 |
  214 |   test('should test responsive dashboard layout', async ({ page }) => {
  215 |     console.log('🧪 Testing responsive dashboard layout...');
  216 |     
  217 |     // Test mobile viewport
  218 |     await page.setViewportSize({ width: 375, height: 667 });
  219 |     await page.waitForTimeout(1000);
  220 |     
  221 |     // Check if KPI cards stack properly on mobile
  222 |     const kpiGrid = page.locator('.grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-4');
  223 |     await expect(kpiGrid).toBeVisible();
  224 |     console.log('✅ Mobile layout responsive');
  225 |     
  226 |     // Test tablet viewport
```