# Test info

- Name: Dashboard KPI Sync Tests >> should display synced KPI data for director
- Location: /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-sync.test.js:31:3

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('h1:has-text("Dashboard")')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('h1:has-text("Dashboard")')

    at /Users/<USER>/Sale bán lẻ/retail-sales-pulse-ios/packages/web/tests/dashboard-sync.test.js:41:34
```

# Test source

```ts
   1 | // PLW: Test dashboard sync functionality với KPI từ tasks và reports
   2 | import { test, expect } from '@playwright/test';
   3 |
   4 | test.describe('Dashboard KPI Sync Tests', () => {
   5 |   test.beforeEach(async ({ page }) => {
   6 |     // Navigate và auto-login
   7 |     await page.goto('http://localhost:8088');
   8 |     await page.waitForLoadState('networkidle');
   9 |     
   10 |     // Setup test auth
   11 |     await page.evaluate(() => {
   12 |       const testUser = {
   13 |         id: 'test-user-001',
   14 |         name: 'Khổng Đức Mạnh',
   15 |         email: '<EMAIL>',
   16 |         role: 'director',
   17 |         team: 'Phòng Kinh Doanh',
   18 |         location: 'Hà Nội',
   19 |         password_changed: true,
   20 |       };
   21 |       localStorage.setItem('currentUser', JSON.stringify(testUser));
   22 |       localStorage.setItem('authToken', 'test-auth-token');
   23 |       localStorage.setItem('isAuthenticated', 'true');
   24 |     });
   25 |     
   26 |     await page.reload();
   27 |     await page.waitForLoadState('networkidle');
   28 |     await page.waitForTimeout(3000);
   29 |   });
   30 |
   31 |   test('should display synced KPI data for director', async ({ page }) => {
   32 |     console.log('🧪 Testing director dashboard KPI sync...');
   33 |     
   34 |     // Navigate to dashboard
   35 |     await page.goto('http://localhost:8088/');
   36 |     await page.waitForLoadState('networkidle');
   37 |     await page.waitForTimeout(2000);
   38 |     
   39 |     // Check if dashboard loaded
   40 |     const dashboardTitle = page.locator('h1:has-text("Dashboard")');
>  41 |     await expect(dashboardTitle).toBeVisible();
      |                                  ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
   42 |     console.log('✅ Dashboard loaded');
   43 |     
   44 |     // Check summary stats section
   45 |     const summarySection = page.locator('.bg-gradient-to-r.from-blue-50');
   46 |     await expect(summarySection).toBeVisible();
   47 |     console.log('✅ Summary section visible');
   48 |     
   49 |     // Check KPI cards
   50 |     const kpiCards = page.locator('.bg-white\\/95.backdrop-blur-lg.rounded-\\[20px\\]');
   51 |     const cardCount = await kpiCards.count();
   52 |     console.log(`📊 Found ${cardCount} KPI cards`);
   53 |     expect(cardCount).toBeGreaterThanOrEqual(4); // KTS, KH/CĐT, SBG, Doanh số
   54 |     
   55 |     // Check specific KPI titles for director
   56 |     await expect(page.locator('text=Tổng KTS')).toBeVisible();
   57 |     await expect(page.locator('text=Tổng KH/CĐT')).toBeVisible();
   58 |     await expect(page.locator('text=Tổng SBG')).toBeVisible();
   59 |     await expect(page.locator('text=Tổng Doanh Số')).toBeVisible();
   60 |     console.log('✅ Director KPI titles correct');
   61 |     
   62 |     // Check charts visibility (director should see all charts)
   63 |     const charts = page.locator('.lg\\:col-span-2, .lg\\:col-span-1');
   64 |     const chartCount = await charts.count();
   65 |     console.log(`📈 Found ${chartCount} chart sections`);
   66 |     expect(chartCount).toBeGreaterThan(0);
   67 |     
   68 |     console.log('✅ Director dashboard test passed');
   69 |   });
   70 |
   71 |   test('should test team leader permissions', async ({ page }) => {
   72 |     console.log('🧪 Testing team leader dashboard permissions...');
   73 |     
   74 |     // Change user to team leader
   75 |     await page.evaluate(() => {
   76 |       const teamLeaderUser = {
   77 |         id: 'team-leader-001',
   78 |         name: 'Lương Việt Anh',
   79 |         email: '<EMAIL>',
   80 |         role: 'team_leader',
   81 |         team: 'Nhóm 1',
   82 |         location: 'Hà Nội',
   83 |         password_changed: true,
   84 |       };
   85 |       localStorage.setItem('currentUser', JSON.stringify(teamLeaderUser));
   86 |     });
   87 |     
   88 |     await page.reload();
   89 |     await page.waitForLoadState('networkidle');
   90 |     await page.waitForTimeout(2000);
   91 |     
   92 |     // Check KPI titles for team leader
   93 |     await expect(page.locator('text=KTS Nhóm, text=KH/CĐT Nhóm, text=SBG Nhóm')).toBeVisible();
   94 |     console.log('✅ Team leader KPI titles correct');
   95 |     
   96 |     // Team leader should see some charts but not all
   97 |     const charts = page.locator('.lg\\:col-span-2, .lg\\:col-span-1');
   98 |     const chartCount = await charts.count();
   99 |     console.log(`📈 Team leader sees ${chartCount} chart sections`);
  100 |     
  101 |     console.log('✅ Team leader dashboard test passed');
  102 |   });
  103 |
  104 |   test('should test employee permissions', async ({ page }) => {
  105 |     console.log('🧪 Testing employee dashboard permissions...');
  106 |     
  107 |     // Change user to employee
  108 |     await page.evaluate(() => {
  109 |       const employeeUser = {
  110 |         id: 'employee-001',
  111 |         name: 'Nguyễn Văn A',
  112 |         email: '<EMAIL>',
  113 |         role: 'employee',
  114 |         team: 'Nhóm 1',
  115 |         location: 'Hà Nội',
  116 |         password_changed: true,
  117 |       };
  118 |       localStorage.setItem('currentUser', JSON.stringify(employeeUser));
  119 |     });
  120 |     
  121 |     await page.reload();
  122 |     await page.waitForLoadState('networkidle');
  123 |     await page.waitForTimeout(2000);
  124 |     
  125 |     // Check KPI titles for employee (should show personal data)
  126 |     await expect(page.locator('text=KTS Cá nhân, text=KH/CĐT Cá nhân, text=SBG Cá nhân')).toBeVisible();
  127 |     console.log('✅ Employee KPI titles correct');
  128 |     
  129 |     // Employee should see limited charts
  130 |     const advancedCharts = page.locator('.lg\\:col-span-2');
  131 |     const advancedChartCount = await advancedCharts.count();
  132 |     console.log(`📈 Employee sees ${advancedChartCount} advanced charts`);
  133 |     
  134 |     console.log('✅ Employee dashboard test passed');
  135 |   });
  136 |
  137 |   test('should verify KPI data synchronization', async ({ page }) => {
  138 |     console.log('🧪 Testing KPI data synchronization...');
  139 |     
  140 |     // Go to tasks page first to create/complete some tasks
  141 |     await page.goto('http://localhost:8088/tasks');
```