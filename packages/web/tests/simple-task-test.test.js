// Simple focused test for task deletion functionality
import { test, expect } from '@playwright/test';

test.describe('Task Delete Test', () => {
  test('should test task deletion with auto-login', async ({ page }) => {
    console.log('🧪 Starting task deletion test...');
    
    // Navigate to application
    await page.goto('http://localhost:8088');
    await page.waitForLoadState('networkidle');
    
    // Auto-login by clicking user and login button
    try {
      console.log('🔐 Attempting auto-login...');
      
      // Wait for login page to load
      await page.waitForSelector('button:has-text("<PERSON><PERSON>ng <PERSON>ập")', { timeout: 10000 });
      
      // Select user (first available user button)
      const userButtons = page.locator('button').filter({ hasText: 'Khổng <PERSON>' });
      if (await userButtons.count() > 0) {
        await userButtons.first().click();
        console.log('✅ User selected');
      }
      
      // Click login
      await page.click('button:has-text("<PERSON><PERSON>ng <PERSON>")');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      console.log('✅ Login completed');
      
      // Take screenshot after login
      await page.screenshot({ path: 'after-login.png', fullPage: true });
      
    } catch (error) {
      console.log(`❌ Login failed: ${error.message}`);
      return;
    }
    
    // Navigate to tasks
    try {
      console.log('🧭 Navigating to tasks...');
      
      // Try multiple navigation strategies
      const currentUrl = page.url();
      console.log(`Current URL: ${currentUrl}`);
      
      // Strategy 1: Look for sidebar navigation
      const sidebarLinks = await page.locator('a, button').all();
      let taskNavFound = false;
      
      for (const link of sidebarLinks) {
        const text = await link.textContent();
        if (text && (text.includes('Công việc') || text.includes('Task') || text.includes('task'))) {
          console.log(`Found task navigation: "${text}"`);
          await link.click();
          taskNavFound = true;
          break;
        }
      }
      
      // Strategy 2: Direct URL navigation if sidebar fails
      if (!taskNavFound) {
        console.log('Trying direct URL navigation...');
        await page.goto('http://localhost:8088/tasks');
        await page.waitForLoadState('networkidle');
      }
      
      await page.waitForTimeout(3000);
      
      // Take screenshot after navigation
      await page.screenshot({ path: 'after-navigation.png', fullPage: true });
      
    } catch (error) {
      console.log(`❌ Navigation failed: ${error.message}`);
    }
    
    // Test task deletion
    try {
      console.log('🗑️ Testing task deletion...');
      
      // Look for task table
      const table = page.locator('table');
      if (await table.isVisible()) {
        console.log('✅ Task table found');
        
        // Count initial tasks
        const taskRows = page.locator('table tbody tr');
        const initialCount = await taskRows.count();
        console.log(`📊 Initial task count: ${initialCount}`);
        
        if (initialCount > 0) {
          // Find delete button
          const deleteButton = taskRows.first().locator('button[title="Xóa"], button:has-text("Xóa")');
          
          if (await deleteButton.isVisible()) {
            console.log('🎯 Delete button found, clicking...');
            
            // Set up dialog handler
            page.once('dialog', async dialog => {
              console.log(`💬 Dialog: ${dialog.message()}`);
              await dialog.accept();
            });
            
            await deleteButton.click();
            await page.waitForTimeout(3000);
            
            // Check if task was deleted
            const finalCount = await taskRows.count();
            console.log(`📊 Final task count: ${finalCount}`);
            
            if (finalCount < initialCount) {
              console.log('✅ Task deletion successful!');
            } else {
              console.log('❌ Task deletion failed - count unchanged');
            }
            
          } else {
            console.log('❌ Delete button not found');
          }
        } else {
          console.log('⚠️ No tasks available for deletion');
        }
        
      } else {
        console.log('❌ Task table not found');
        
        // Debug: log page content
        const pageContent = await page.locator('body').textContent();
        console.log('Page content:', pageContent?.substring(0, 200));
      }
      
    } catch (error) {
      console.log(`❌ Task deletion test failed: ${error.message}`);
    }
    
    console.log('🏁 Test completed');
  });
});
