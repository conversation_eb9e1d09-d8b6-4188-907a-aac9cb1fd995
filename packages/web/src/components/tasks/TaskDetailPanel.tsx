import {
  Calendar,
  Check,
  Clock,
  Edit,
  FileText,
  Minus,
  Plus,
  Save,
  Tag,
  Trash2,
  User,
  X,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import notificationService from '@/services/notificationService';

interface TaskDetailPanelProps {
  task: any;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (task: any) => void;
  onDelete?: (taskId: string) => void;
}

const TaskDetailPanel: React.FC<TaskDetailPanelProps> = ({
  task,
  isOpen,
  onClose,
  onEdit,
  onDelete,
}) => {
  const { currentUser, users } = useAuth();
  const [checklist, setChecklist] = useState<
    Array<{ id: number; text: string; completed: boolean }>
  >([]);
  const [newChecklistItem, setNewChecklistItem] = useState('');
  const [isEditing, setIsEditing] = useState(true);
  const [editedTask, setEditedTask] = useState(task);

  // Function để lấy tên người dùng từ nhiều nguồn
  const getUserName = (task: any) => {
    // Ưu tiên: user_name -> tìm trong users array -> assignedTo -> fallback
    if (task.user_name && task.user_name !== 'Không xác định') {
      return task.user_name;
    }

    // Tìm trong users array bằng user_id
    if (task.user_id && users && users.length > 0) {
      const user = users.find(u => u.id === task.user_id);
      if (user && user.name) {
        return user.name;
      }
    }

    // Tìm trong users array bằng assignedTo
    if (task.assignedTo && users && users.length > 0) {
      const user = users.find(u => u.id === task.assignedTo);
      if (user && user.name) {
        return user.name;
      }
    }

    // Fallback về assignedTo nếu không phải ID
    if (task.assignedTo && task.assignedTo !== 'Không xác định' && !task.assignedTo.includes('-')) {
      return task.assignedTo;
    }

    return 'Chưa xác định';
  };

  // Kiểm tra quyền edit task
  const canEditTask = (task: any) => {
    if (!currentUser) return false;

    // Directors có thể edit tất cả tasks
    if (currentUser.role === 'retail_director' || currentUser.role === 'project_director') {
      return true;
    }

    // Team leaders có thể edit tasks của team members
    if (currentUser.role === 'team_leader') {
      // Có thể edit nếu là người tạo hoặc task được assign cho team member
      const isCreator = task.user_id === currentUser.id;
      const isTeamTask = users.some(user =>
        user.team_id === currentUser.team_id &&
        (user.id === task.assignedTo || user.id === task.user_id)
      );
      return isCreator || isTeamTask;
    }

    // Employees chỉ có thể edit tasks của mình
    return task.user_id === currentUser.id || task.assignedTo === currentUser.id;
  };

  useEffect(() => {
    if (task) {
      setEditedTask({ ...task });
    }
  }, [task]);

  if (!isOpen || !task) return null;

  const statusMapping = {
    'todo': 'Chưa bắt đầu',
    'in-progress': 'Đang thực hiện',
    'on-hold': 'Đang chờ',
    'completed': 'Đã hoàn thành',
  };

  const priorityMapping = {
    high: 'Cao',
    normal: 'Bình thường',
    low: 'Thấp',
  };

  const typeMapping = {
    partner_new: 'Đối tác mới',
    partner_old: 'Đối tác cũ',
    architect_new: 'KTS mới',
    architect_old: 'KTS cũ',
    client_new: 'Khách hàng mới',
    client_old: 'Khách hàng cũ',
    quote_new: 'Báo giá mới',
    quote_old: 'Báo giá cũ',
    other: 'Khác',
  };

  const statusColors = {
    'todo': 'bg-gray-400',
    'in-progress': 'bg-blue-500',
    'on-hold': 'bg-amber-400',
    'completed': 'bg-green-500',
  };

  const priorityColors = {
    high: 'bg-red-500',
    normal: 'bg-yellow-500',
    low: 'bg-green-500',
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  };

  const getProgressFromChecklist = () => {
    if (checklist.length === 0) return 0;
    const completedItems = checklist.filter((item) => item.completed).length;
    return Math.round((completedItems / checklist.length) * 100);
  };

  return (
    <>
      {/* Enhanced Backdrop with stronger blur */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/70 backdrop-blur-lg transition-all duration-500"
          onClick={onClose}
          style={{
            zIndex: 2147483646,
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backdropFilter: 'blur(12px) saturate(180%)',
            WebkitBackdropFilter: 'blur(12px) saturate(180%)',
          }}
        />
      )}

      {/* Panel - responsive */}
      <div
        className={`fixed left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white shadow-2xl transition-all duration-300 ease-out flex flex-col ${isOpen ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}`}
        style={{
          zIndex: 2147483647,
          width: 'min(95vw, 1000px)',
          height: 'min(90vh, 750px)',
          borderRadius: '24px',
          border: '1px solid rgba(0, 0, 0, 0.1)',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header - responsive */}
        <div className="flex-shrink-0 flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-t-[24px]">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <FileText className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Chi tiết công việc</h2>
              <p className="text-blue-100 text-sm opacity-90">Chỉnh sửa và quản lý</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white/20 rounded-lg transition-all duration-200 group"
          >
            <X className="w-5 h-5 text-white group-hover:rotate-90 transition-transform duration-200" />
          </button>
        </div>

        {/* Content - Scrollable */}
        <div className="flex-1 overflow-y-auto min-h-0">
          {/* Title & Status Section - responsive */}
          <div className="p-6 bg-gray-50 border-b border-gray-200">
            <div className="relative mb-4">
              <input
                type="text"
                value={editedTask?.title || ''}
                onChange={(e) => setEditedTask((prev) => ({ ...prev, title: e.target.value }))}
                disabled={!canEditTask(task)}
                className={`text-xl font-bold text-gray-900 w-full border-0 border-b-2 bg-transparent px-0 py-3 focus:outline-none transition-colors duration-200 placeholder-gray-400 ${
                  canEditTask(task)
                    ? 'border-gray-200 focus:border-blue-500 cursor-text'
                    : 'border-gray-100 cursor-not-allowed opacity-60'
                }`}
                placeholder={canEditTask(task) ? "Nhập tiêu đề công việc..." : "Bạn không có quyền chỉnh sửa"}
              />
            </div>

            {/* Meta Info - responsive */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex flex-wrap items-center gap-3">
                <div className="flex items-center space-x-2 bg-white px-3 py-2 rounded-lg border border-gray-200">
                  <Calendar className="w-4 h-4 text-blue-500" />
                  <span className="text-gray-700 font-medium text-sm">{formatDate(task.date)}</span>
                </div>
                {task.time && (
                  <div className="flex items-center space-x-2 bg-white px-3 py-2 rounded-lg border border-gray-200">
                    <Clock className="w-4 h-4 text-green-500" />
                    <span className="text-gray-700 font-medium text-sm">{task.time}</span>
                  </div>
                )}
                <div className="flex items-center space-x-2 bg-white px-3 py-2 rounded-lg border border-gray-200">
                  <User className="w-4 h-4 text-purple-500" />
                  <span className="text-gray-700 font-medium text-sm">{getUserName(task)}</span>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div
                  className={`px-4 py-2 text-sm font-semibold rounded-full text-white ${statusColors[task.status]}`}
                >
                  {statusMapping[task.status]}
                </div>
                <div
                  className={`px-4 py-2 text-sm font-semibold rounded-full text-white ${priorityColors[task.priority || 'normal']}`}
                >
                  {priorityMapping[task.priority || 'normal']}
                </div>
              </div>
            </div>

            {/* Progress Bar - Dựa trên checklist */}
            <div className="mt-4 bg-white rounded-lg p-4 border border-gray-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-semibold text-gray-800">Tiến độ hoàn thành</span>
                <span className="text-sm font-bold text-blue-600">
                  {getProgressFromChecklist()}%
                </span>
              </div>
              <div className="relative w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <div
                  className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getProgressFromChecklist()}%` }}
                ></div>
              </div>
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-gray-600">
                  {checklist.filter((item) => item.completed).length} / {checklist.length} hoàn thành
                </span>
                <span className="text-xs text-gray-500">Dựa trên checklist</span>
              </div>
            </div>
          </div>

          {/* Description Section - responsive */}
          <div className="p-6 flex-1">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                <FileText className="w-4 h-4 text-white" />
              </div>
              <div>
                <h4 className="text-lg font-bold text-gray-900">Nội dung công việc</h4>
                <p className="text-sm text-gray-500">Mô tả chi tiết về công việc</p>
              </div>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6 min-h-[200px]">
              <textarea
                value={editedTask?.description || ''}
                onChange={(e) =>
                  setEditedTask((prev) => ({ ...prev, description: e.target.value }))
                }
                disabled={!canEditTask(task)}
                className={`w-full h-44 leading-relaxed text-sm resize-none border-none bg-transparent focus:outline-none placeholder-gray-400 ${
                  canEditTask(task)
                    ? 'text-gray-700 cursor-text'
                    : 'text-gray-500 cursor-not-allowed opacity-60'
                }`}
                placeholder={canEditTask(task) ? "Nhập mô tả chi tiết về công việc, yêu cầu, mục tiêu..." : "Bạn không có quyền chỉnh sửa nội dung"}
              />
            </div>

            {/* Checklist Section */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <Check className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900">Checklist công việc</h4>
                    <p className="text-sm text-gray-500">Theo dõi tiến độ từng bước</p>
                  </div>
                </div>
                <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  {checklist.filter((item) => item.completed).length}/{checklist.length}
                </div>
              </div>

              {/* Checklist Items */}
              <div className="space-y-2 mb-4">
                {checklist.map((item) => (
                  <div key={item.id} className="flex items-center group bg-white rounded-lg p-3 border border-gray-200 hover:shadow-sm transition-all duration-200">
                    <button
                      onClick={() => {
                        setChecklist((prev) =>
                          prev.map((i) =>
                            i.id === item.id ? { ...i, completed: !i.completed } : i,
                          ),
                        );
                      }}
                      className={`w-5 h-5 rounded-lg border-2 mr-3 flex items-center justify-center transition-all duration-200 ${
                        item.completed
                          ? 'bg-green-500 border-green-500 text-white'
                          : 'border-gray-300 hover:border-green-400 hover:bg-green-50'
                      }`}
                    >
                      {item.completed && <Check className="w-3 h-3" />}
                    </button>
                    <span
                      className={`flex-1 text-sm transition-all duration-200 ${
                        item.completed
                          ? 'line-through text-gray-500'
                          : 'text-gray-700'
                      }`}
                    >
                      {item.text}
                    </span>
                    <button
                      onClick={() => {
                        setChecklist((prev) => prev.filter((i) => i.id !== item.id));
                      }}
                      className="opacity-0 group-hover:opacity-100 p-1 text-red-500 hover:bg-red-50 rounded-lg transition-all duration-200"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>

              {/* Add New Item */}
              <div className="flex items-center space-x-3 bg-white rounded-lg p-3 border border-gray-200">
                <div className="w-5 h-5 bg-blue-500 rounded-lg flex items-center justify-center">
                  <Plus className="w-3 h-3 text-white" />
                </div>
                <input
                  type="text"
                  value={newChecklistItem}
                  onChange={(e) => setNewChecklistItem(e.target.value)}
                  placeholder="Thêm mục mới vào checklist..."
                  className="flex-1 bg-transparent border-none text-sm focus:outline-none placeholder-gray-400"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && newChecklistItem.trim()) {
                      setChecklist((prev) => [
                        ...prev,
                        {
                          id: Date.now(),
                          text: newChecklistItem.trim(),
                          completed: false,
                        },
                      ]);
                      setNewChecklistItem('');
                    }
                  }}
                />
                <button
                  onClick={() => {
                    if (newChecklistItem.trim()) {
                      setChecklist((prev) => [
                        ...prev,
                        {
                          id: Date.now(),
                          text: newChecklistItem.trim(),
                          completed: false,
                        },
                      ]);
                      setNewChecklistItem('');
                    }
                  }}
                  className="px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-200 text-sm"
                >
                  Thêm
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Actions - responsive */}
        <div className="flex-shrink-0 border-t border-gray-200 p-4 bg-gray-50 rounded-b-[24px]">
          <div className="flex gap-3">
            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();

                if (!canEditTask(task)) {
                  alert('Bạn không có quyền chỉnh sửa công việc này!');
                  return;
                }

                console.log('💾 SAVING TASK!', editedTask);

                if (currentUser && task) {
                  const changes = [];
                  if (editedTask.title !== task.title) changes.push('tiêu đề');
                  if (editedTask.description !== task.description) changes.push('mô tả');

                  if (changes.length > 0) {
                    notificationService.updateTaskNotification(
                      task.id,
                      editedTask.title || task.title || 'Công việc không có tiêu đề',
                      currentUser.id,
                      currentUser.name,
                      changes.join(', '),
                    );
                  }
                }

                onEdit && onEdit(editedTask);
                alert('Đã lưu công việc thành công!');
                onClose();
              }}
              disabled={!canEditTask(task)}
              className={`flex-1 h-10 text-sm font-semibold rounded-lg ${
                canEditTask(task)
                  ? 'bg-green-500 hover:bg-green-600 text-white'
                  : 'bg-gray-400 text-gray-600 cursor-not-allowed opacity-60'
              }`}
              type="button"
            >
              <Save className="w-4 h-4 mr-2" />
              {canEditTask(task) ? 'Lưu công việc' : 'Không có quyền'}
            </Button>
            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();

                if (!canEditTask(task)) {
                  alert('Bạn không có quyền xóa công việc này!');
                  return;
                }

                console.log('🔴 DETAIL PANEL DELETE CLICKED!', task.id);
                onDelete && onDelete(task.id);
              }}
              disabled={!canEditTask(task)}
              variant="outline"
              className={`flex-1 h-10 text-sm font-semibold rounded-lg ${
                canEditTask(task)
                  ? 'border border-red-300 text-red-600 hover:bg-red-50'
                  : 'border border-gray-200 text-gray-400 cursor-not-allowed opacity-60'
              }`}
              type="button"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {canEditTask(task) ? 'Xóa công việc' : 'Không có quyền'}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default TaskDetailPanel;
