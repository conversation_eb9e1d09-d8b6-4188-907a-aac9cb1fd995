// Simple fallback dashboard để tránh crash
import React from 'react';
import { BarChart3, TrendingUp, Users, DollarSign } from 'lucide-react';
import { User } from '@/types/user';

interface SimpleDashboardProps {
  currentUser: User | null;
  isLoading?: boolean;
}

const SimpleDashboard: React.FC<SimpleDashboardProps> = ({ currentUser, isLoading = false }) => {
  if (isLoading) {
    return (
      <div className="p-4 md:p-6 space-y-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const isDirector = currentUser?.name === 'Khổng Đức Mạnh';
  const isTeamLeader = currentUser?.role === 'team_leader';

  const kpiCards = [
    {
      title: isDirector ? 'Tổng KTS' : isTeamLeader ? 'KTS Nhóm' : 'KTS Cá nhân',
      value: '0',
      icon: BarChart3,
      color: 'blue'
    },
    {
      title: isDirector ? 'Tổng KH/CĐT' : isTeamLeader ? 'KH/CĐT Nhóm' : 'KH/CĐT Cá nhân',
      value: '0',
      icon: Users,
      color: 'green'
    },
    {
      title: isDirector ? 'Tổng SBG' : isTeamLeader ? 'SBG Nhóm' : 'SBG Cá nhân',
      value: '0',
      icon: TrendingUp,
      color: 'purple'
    },
    {
      title: isDirector ? 'Tổng Doanh Số' : isTeamLeader ? 'Doanh Số Nhóm' : 'Doanh Số Cá nhân',
      value: '0',
      icon: DollarSign,
      color: 'yellow'
    }
  ];

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return 'bg-blue-50 border-blue-200 text-blue-600';
      case 'green':
        return 'bg-green-50 border-green-200 text-green-600';
      case 'purple':
        return 'bg-purple-50 border-purple-200 text-purple-600';
      case 'yellow':
        return 'bg-yellow-50 border-yellow-200 text-yellow-600';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-600';
    }
  };

  return (
    <div className="p-4 md:p-6 space-y-8">
      {/* Summary */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100 p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Dashboard đang được cập nhật
          </h3>
          <p className="text-sm text-gray-600">
            Dữ liệu KPI sẽ hiển thị khi có công việc và báo cáo được tạo.
          </p>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6" data-testid="kpi-cards-grid">
        {kpiCards.map((card, index) => {
          const Icon = card.icon;
          const colorClasses = getColorClasses(card.color);
          
          return (
            <div
              key={index}
              className={`rounded-xl border p-6 transition-all duration-200 hover:shadow-md ${colorClasses}`}
            >
              <div className="flex items-center justify-between mb-4">
                <Icon className="w-6 h-6" />
                <span className="text-xs font-medium opacity-75">KPI</span>
              </div>
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium opacity-90">{card.title}</h4>
                <div className="text-2xl font-bold">{card.value}</div>
                <div className="text-xs opacity-75">
                  Chưa có dữ liệu
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Info Section */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="text-center text-gray-500">
          <BarChart3 className="w-12 h-12 mx-auto mb-3 text-gray-400" />
          <h4 className="font-medium mb-2">Bắt đầu sử dụng Dashboard</h4>
          <p className="text-sm">
            Tạo công việc và báo cáo để xem dữ liệu KPI chi tiết tại đây.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SimpleDashboard;
