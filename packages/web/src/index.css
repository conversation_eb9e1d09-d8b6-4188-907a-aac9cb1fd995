@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import './styles/dialog-fixes.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.8);
}

/* Task form dialog animations */
.task-form-dialog {
  animation: slideInUp 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translate(-50%, -40%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Enhanced animations with hardware acceleration */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translate3d(0, 10px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale3d(0.95, 0.95, 1);
  }
  to {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translate3d(-10px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 212 100% 52%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 212 100% 52%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
    font-feature-settings: 'ss01', 'ss02', 'ss03';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* SF Pro Fonts */
@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-regular-webfont.woff2')
    format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-medium-webfont.woff2')
    format('woff2');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-semibold-webfont.woff2')
    format('woff2');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-bold-webfont.woff2')
    format('woff2');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscotext-regular-webfont.woff2')
    format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscotext-medium-webfont.woff2')
    format('woff2');
  font-weight: 500;
  font-style: normal;
}

/* macOS-style utility classes */
@layer components {
  .macos-card {
    @apply bg-white/90 backdrop-blur-xl rounded-xl shadow-md border border-white/10 overflow-hidden;
  }

  .macos-button {
    @apply bg-ios-blue text-white font-medium rounded-lg px-4 py-2 transition-all hover:bg-ios-blue/90 active:scale-[0.98] shadow-sm;
  }

  .macos-button-secondary {
    @apply bg-white/80 text-ios-dark font-medium rounded-lg border border-gray-200/50 px-4 py-2 transition-all hover:bg-white/100 active:scale-[0.98] shadow-sm;
  }

  .macos-input {
    @apply bg-white/80 border border-gray-200/50 rounded-lg px-4 py-3 w-full focus:ring-2 focus:ring-ios-blue focus:outline-none transition-all;
  }

  .macos-select {
    @apply bg-white/80 border border-gray-200/50 rounded-lg px-4 py-3 w-full focus:ring-2 focus:ring-ios-blue focus:outline-none transition-all;
  }

  .blur-background {
    @apply backdrop-blur-xl bg-white/80 dark:bg-[#1c1c1e]/80 border border-white/10 dark:border-white/5;
  }

  .ios-card {
    @apply bg-white/90 backdrop-blur-xl rounded-xl shadow-md border border-white/10 overflow-hidden;
  }

  .ios-button {
    @apply bg-ios-blue text-white font-medium rounded-lg px-4 py-2 transition-all hover:bg-ios-blue/90 active:scale-[0.98] shadow-sm;
  }

  .ios-button-secondary {
    @apply bg-white/80 text-ios-dark font-medium rounded-lg border border-gray-200/50 px-4 py-2 transition-all hover:bg-white/100 active:scale-[0.98] shadow-sm;
  }

  .ios-input {
    @apply bg-white/80 border border-gray-200/50 rounded-lg px-4 py-3 w-full focus:ring-2 focus:ring-ios-blue focus:outline-none transition-all;
  }
}

/* Animation for background gradient */
@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient-animation {
  animation: gradient-animation 15s ease infinite;
}

/* Pattern background */
.bg-pattern {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 20%, transparent 20%);
  background-size: 30px 30px;
}

/* Float animation */
@keyframes float {
  0% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(-50px, 50px);
  }
  100% {
    transform: translate(0, 0);
  }
}

.animate-float {
  animation: float 20s linear infinite;
}

/* Shimmer effect */
@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 1.5s infinite;
}

/* Modal Styles - Fix positioning issues */
.modal-overlay,
.account-settings-modal {
  position: fixed !important;
  z-index: 99999 !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

.modal-content,
.account-settings-content {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 100000 !important;
  max-height: 80vh !important;
  max-width: 90vw !important;
  overflow-y: auto !important;
  background: #ffffff !important;
  border-radius: 16px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.modal-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  z-index: 99998 !important;
}

body.modal-open {
  overflow: hidden !important;
}

.top-navigation,
.bottom-navigation,
.sidebar {
  z-index: 1000 !important;
}

@media (max-width: 768px) {
    .modal-content,
    .account-settings-content {
        width: 95vw !important;
        max-height: 95vh !important;
        margin: 0 !important;
    }

    /* Hide sidebar on mobile and show bottom navigation instead */
    .sidebar-desktop {
        display: none !important;
    }

    /* Adjust main content margin on mobile */
    main {
        margin-left: 0 !important;
        padding-bottom: 4rem !important;
    }

    /* Show bottom navigation on mobile */
    .bottom-nav-mobile {
        display: flex !important;
    }
}

@media (min-width: 769px) {
    /* Hide bottom navigation on desktop */
    .bottom-nav-mobile {
        display: none !important;
    }

    /* Main content margin is controlled by Tailwind classes in AppLayout */
    /* Remove the !important override to let Tailwind classes work */
    main {
        padding-bottom: 0 !important;
        transition: margin-left 0.3s ease-in-out !important;
    }
}

/* Optimized sidebar animations with hardware acceleration */
.sidebar-desktop {
    overflow: visible !important;
    transform: translateZ(0); /* Force hardware acceleration */
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Optimized main content transitions */
main {
    transform: translateZ(0); /* Force hardware acceleration */
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Account popover positioning */
[data-radix-popper-content-wrapper] {
    z-index: 1000 !important;
}

[data-radix-popover-content] {
    z-index: 1000 !important;
}

/* Notification Center - Proper positioning */
div[data-notification="center"] {
    z-index: 99999 !important;
    position: fixed !important;
}

/* Notification panel styling */
.notification-panel,
.notification-center-panel {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

/* Mobile responsive notification positioning */
@media (max-width: 640px) {
    div[data-notification="center"],
    body > [data-notification="center"] {
        top: 70px !important;
        right: 8px !important;
        left: 8px !important;
        width: auto !important;
        max-width: calc(100vw - 16px) !important;
    }
}

/* Ensure notification is always on top */
div[data-notification="center"],
.notification-panel,
.notification-center-panel {
    z-index: 99999 !important;
    position: fixed !important;
}

/* Prevent cards from having higher z-index than notifications */
.bg-white\/95,
.bg-white\/90,
.bg-white\/80,
div[class*="Card"],
div[class*="card"],
.macos-card,
.ios-card {
    z-index: auto !important;
    position: relative !important;
}

/* Force notification to be above everything */
body > div:has([data-notification="center"]),
div:has([data-notification="center"]) {
    z-index: 99999 !important;
}

/* Ensure main content doesn't interfere */
main,
main > div,
.p-4,
.p-6,
.space-y-8,
.grid {
    z-index: 0 !important;
    position: relative !important;
}

/* Force all content containers to have low z-index */
.flex-1,
.overflow-y-auto,
.min-h-screen,
div[class*="bg-gradient"],
div[class*="backdrop-blur"] {
    z-index: 0 !important;
}

/* Override table sticky headers and cells that might interfere */
table th[style*="position: sticky"],
table td[style*="position: sticky"],
th[style*="zIndex"],
td[style*="zIndex"] {
    z-index: 5 !important;
}

/* Force all tables and their content to have low z-index */
table,
thead,
tbody,
tr,
th,
td {
    z-index: auto !important;
}

/* Ultimate notification override - force above everything */
[data-notification="center"] {
    z-index: 999999 !important;
    position: fixed !important;
}

/* Ensure notification backdrop is also high */
div[class*="fixed"][class*="inset-0"]:has(+ [data-notification="center"]) {
    z-index: 999998 !important;
}

/* Portal notification styles - Ultimate override */
body > [data-notification="center"] {
    z-index: 2147483647 !important;
    position: fixed !important;
}

/* Portal backdrop styles */
body > div[style*="z-index: 2147483646"] {
    z-index: 2147483646 !important;
    position: fixed !important;
}

/* Task Detail Modal - Force above everything except notifications */
div[style*="z-index: 999999"] {
    z-index: 999999 !important;
    position: fixed !important;
}

/* Task Detail Modal Backdrop */
div[style*="z-index: 999998"] {
    z-index: 999998 !important;
    position: fixed !important;
}

/* Force all task list content to have lower z-index */
.space-y-6,
.space-y-4,
table,
thead,
tbody,
tr,
th,
td,
.table-header,
.task-list,
.task-group,
.task-card {
    z-index: 1 !important;
    position: relative !important;
}

/* Fix dropdown styling in TaskDetailPanel */
.task-detail-panel select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 8px center !important;
    background-repeat: no-repeat !important;
    background-size: 12px 12px !important;
    padding-right: 28px !important;
}

.task-detail-panel select option {
    color: #1f2937 !important;
    background-color: #ffffff !important;
    padding: 8px 12px !important;
    font-weight: 500 !important;
}

/* Account Settings Dialog - Force above everything */
[data-radix-dialog-overlay] {
    z-index: 999999 !important;
    position: fixed !important;
}

[data-radix-dialog-content] {
    z-index: 999999 !important;
    position: fixed !important;
}

/* Ensure dialog portal is above sidebar */
div[data-radix-portal] {
    z-index: 999999 !important;
}

/* Fix for select dropdown in dialog */
[data-radix-select-content] {
    z-index: 1000000 !important;
    position: fixed !important;
}

[data-radix-popper-content-wrapper] {
    z-index: 1000000 !important;
}

/* Ensure select trigger is clickable in dialog */
[data-radix-select-trigger] {
    position: relative !important;
    z-index: 10 !important;
}

/* Fix dialog overlay pointer events */
[data-radix-dialog-overlay] {
    pointer-events: auto !important;
    background: rgba(0, 0, 0, 0.4) !important;
    backdrop-filter: blur(4px) !important;
}

[data-radix-dialog-content] {
    pointer-events: auto !important;
    background: white !important;
    border: none !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

/* Task Form Dialog specific fixes */
[data-radix-dialog-content] {
    background: white !important;
    opacity: 1 !important;
    filter: none !important;
    z-index: 10000 !important;
    position: fixed !important;
    visibility: visible !important;
    display: grid !important;
}

[data-radix-dialog-overlay] {
    background: rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(2px) !important;
    z-index: 9999 !important;
    position: fixed !important;
    visibility: visible !important;
}

/* Force dialog portal to be visible */
[data-radix-portal] {
    z-index: 10001 !important;
    position: fixed !important;
}

/* Ensure dialog is always on top */
div[data-radix-dialog-content] {
    z-index: 10000 !important;
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}
