import React from 'react';

import KpiDashboard from '@/components/dashboard/KpiDashboard';
import AppLayout from '@/components/layout/AppLayout';
import PageHeader from '@/components/layout/PageHeader';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
import { useTaskData } from '@/hooks/use-task-data';
import { getDashboardSubtitle } from '@/utils/kpiUtils';
import { dashboardSyncService } from '@/services/DashboardSyncService';

const Index = () => {
  const { currentUser } = useAuth();
  const { tasks, isLoading } = useTaskData();

  // Lấy dữ liệu dashboard đồng bộ từ tasks và reports
  const dashboardData = dashboardSyncService.getSyncedDashboardData(currentUser, tasks);

  // Debug logging
  console.log('📊 Dashboard Index - Synced data:', {
    user: currentUser?.name,
    role: currentUser?.role,
    tasksCount: tasks.length,
    kpiCardsCount: dashboardData.kpiCards.length,
    permissions: dashboardData.permissions,
    summary: dashboardData.summary,
    isLoading
  });

  // Lấy tiêu đề phụ dựa trên loại người dùng và quyền hạn
  const subtitle = getDashboardSubtitle(currentUser);
  const enhancedSubtitle = dashboardData.permissions.canViewAll
    ? `${subtitle} - Toàn phòng ban`
    : dashboardData.permissions.canViewTeam
    ? `${subtitle} - Nhóm ${currentUser?.location}`
    : subtitle;

  return (
    <AppLayout>
      <PageHeader
        title="Dashboard"
        subtitle={enhancedSubtitle}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              📊 Báo cáo chi tiết
            </Button>
            <Button size="sm">
              📤 Xuất báo cáo
            </Button>
          </div>
        }
      />

      <KpiDashboard
        kpiData={dashboardData.kpiCards}
        currentUser={currentUser}
        dashboardData={dashboardData}
        isLoading={isLoading}
      />
    </AppLayout>
  );
};

export default Index;
